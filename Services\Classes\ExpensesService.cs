using InnoBook.Common;
using InnoBook.DTO.Attachement;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Expenses;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Request.Expenses;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.Design;

namespace InnoBook.Services.Classes
{
    public class ExpensesService(InnoLogicielContext _context, IConfiguration config) : IExpensesService
    {
        public async Task<RequestExpenses> CreateExpenses(RequestExpenses expenses, string userId, string companyId)
        {
            if (expenses.Attachments != null && expenses.Attachments.Count > 0)
            {
                expenses.Attachments.ForEach(x =>
                {
                    x.CreatedBy = userId;
                    x.CreatedAt = DateTime.UtcNow;
                });
            }

            if (expenses.ItemExpense != null && expenses.ItemExpense.Count > 0)
            {
                expenses.ItemExpense.SelectMany(c=>c.Taxes).ToList().ForEach(t =>
                {
                    t.CompanyTaxId = t.Id;
                    t.ItemExpenseId = t.Id;
                    t.Id = Guid.Empty;
                });
            }
            var newExpenses = new Expenses
            {
                ExpensesName=expenses.ExpensesName,
                CompanyId = Guid.Parse(companyId),
                ClientId = expenses.ClientId,
                CreatedBy=userId,
                ProjectId = expenses.ProjectId,
                CategoryId = expenses.CategoryId,
                MerchantId = expenses.MerchantId,
                CategoryItemId = expenses.CategoryItemId,
                Note = expenses.Note,
                Date=expenses.Date,
                Attachments=expenses.Attachments,
                ItemExpense = expenses.ItemExpense,
                PaidAmount = expenses.PaidAmount,
            };

            _context.Expenses.Add(newExpenses);
            await _context.SaveChangesAsync();

            if (expenses.Attachments != null && expenses.Attachments.Count() > 0)
            {
                expenses.Attachments.ForEach(async (file) =>
                {
                    var fileConvert = DigitalOcean.Base64ToIFormFile(file.base64, file.Filename, file.Type);
                    var result = await DigitalOcean.UploadFileAsync(fileConvert, config, companyId);
                });
            }

            return expenses;
        }

        public async Task<bool> DeleteExpenses(List<Guid?> listExpenses, string idUser)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    // TODO: Change to soft delete avoid missing data
                    var listItemInvoiveRemove = _context.ItemInvoice.Where(x => listExpenses.Contains(x.ExpensesId)).ToList();
                    _context.ItemInvoice.RemoveRange(listItemInvoiveRemove);
                    var listItemExpensesRemove = _context.ItemExpense.Where(x => listExpenses.Contains(x.ExpensesId)).ToList();
                    _context.ItemExpense.RemoveRange(listItemExpensesRemove);
                    await _context.SaveChangesAsync();
                    var listItemAttchRemove = _context.Attachment.Where(x => listExpenses.Contains(x.ExpensesId)).ToList();
                    _context.Attachment.RemoveRange(listItemAttchRemove);
                    await _context.SaveChangesAsync();
                    var listItemDetailRemove = _context.Expenses.Where(x => listExpenses.Contains(x.Id)).ToList();
                    _context.Expenses.RemoveRange(listItemDetailRemove);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        public async Task<PaginatedResponse<DTOExpenses>> GetAllExpenses(GetExpenseRequestParam filter, string companyId, string userId)
        {
            var query = _context.Expenses
                                .Include(x => x.Category)
                                .Include(x => x.CategoryItem)
                                .Include(x => x.Client)
                                .Include(x => x.Project)
                                   .ThenInclude(c => c.Members)
                                .Where(x => x.CompanyId.ToString() == companyId && x.isActive && x.Client.isActive 
                                            && (x.Project == null || (x.Project.isActive && !x.Project.isArchive)));
            // Client
            if (filter.ClientId != null)
            {
                query = query.Where(x => x.ClientId.ToString() == filter.ClientId.ToString());
            }

            // Project
            if (filter.ProjectId != null)
            {
                query = query.Where(x => x.ProjectId.ToString() == filter.ProjectId.ToString());
            }

            // Search
            if (filter.Search != null && !string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(c => c.ExpensesName.ToLower().Contains(filter.Search.ToLower())
                                         || c.Project.ProjectName.ToLower().Contains(filter.Search.ToLower())
                                         || c.Client.ClientName.ToLower().Contains(filter.Search.ToLower())
                                         || c.Category.CategoryName.ToLower().Contains((filter.Search.ToLower())));
            }

            // Filter DateTime
            if (!string.IsNullOrEmpty(filter.TimeZone) && !string.IsNullOrEmpty(filter.FilterDate) && DateTime.TryParse(filter.FilterDate, out var date))
            {
                var utcDate = Utils.GetStartAndEndOfUtcDate(date, filter.TimeZone);
                query = query.Where(x => x.Date >= utcDate.StartUtcDateTime && x.Date < utcDate.EndUtcDateTime);
            }

            // Filter Status
            if (filter.Status != null)
            {
                query = query.Where(c => c.Status == filter.Status);
            }

            // Filter project Id
            if (filter.ProjectIds != null && filter.ProjectIds.Count > 0) 
            { 
                query = query.Where(c => filter.ProjectIds.Contains(c.ProjectId.ToString()));
            }

            // Get assigned project
            if (filter.Role != UserBusinessRole.Admin)
            {
                // Restrict normal users only get the assigned projects
                query = query.Where(c => c.Project == null || c.Project.Members.Select(m => m.UserId).Contains(Guid.Parse(userId)));
            }

            int totalRecords = await query.CountAsync();

            var panigationQuery = query.AsNoTracking().OrderByDescending(x => x.CreatedAt)
                                        .Select(x => new DTOExpenses
                                        {
                                            ExpensesName = x.ExpensesName,
                                            ClientName = x.Client.ClientName,
                                            ProjectName = x.Project.ProjectName,
                                            CategoryName = x.Category.CategoryName,
                                            itemName = x.CategoryItem.ItemName,
                                            createdBy = x.CreatedBy,
                                            Note = x.Note,
                                            Id = x.Id,
                                            Date = x.Date,
                                            Status = x.Status == 0 ? 1 : x.Status,
                                            PaidAmount = x.PaidAmount,
                                            CreateAt = x.CreatedAt,
                                        })
                                        .OrderByDescending(x => x.CreateAt)
                                        .AsQueryable();

            // Sort
            if (filter.ColumnName != null && filter.Direction != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
            }

            // Panigation query
            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var data = await panigationQuery.ToListAsync();

            // Mapping created user
            var createdUsers = data.Where(c => !string.IsNullOrEmpty(c.createdBy))
                                   .Select(x => Guid.Parse(x.createdBy)).Distinct().ToList();
            var users = await _context.Users.Where(c=> createdUsers.Contains(c.Id))
                                            .Select(x => new UserExpensesDTO
                                            {
                                                Id = x.Id.ToString(),
                                                FirstName = x.FirstName,
                                                LastName = x.LastName,
                                                Email = x.Email
                                            }).ToListAsync();
            data.ForEach(c =>
            {
                c.inforUser = users.FirstOrDefault(x => x.Id == c.createdBy);
            });

            return new PaginatedResponse<DTOExpenses>
            {
                PageSize = filter.PageSize,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                Data = data,
                TotalRecords = totalRecords

            };
        }

        public async Task<PaginatedResponse<AttachmentDTO>> GetAllUploadExpenses(GetUploadedAttachmentParam filter)
        {
            var query = _context.Attachment
                                .Include(x => x.Expenses)
                                  .ThenInclude(x => x.Project)
                                    .ThenInclude(x => x.Members)
                                .Where(x => x.Expenses != null && x.Expenses.CompanyId.ToString() == filter.CompanyId 
                                            && x.ExpensesId == Guid.Parse(filter.ExpensesId));

            if (filter.Role != UserBusinessRole.Admin)
            {
                query = query.Where(c => c.Expenses.Project == null || c.Expenses.Project.Members.Select(m => m.UserId).Contains(Guid.Parse(filter.UserId)));
            }

            int totalRecords = await query.CountAsync();

            var panigationQuery =  query.AsNoTracking()
                             .Select(x => new AttachmentDTO
                             {
                                 Id = x.Id,
                                 Filename = x.Filename,
                                 Size = (decimal)x.Size,
                                 Type = x.Type,
                                 CreatedAt = x.CreatedAt,

                             })
                             .OrderByDescending(x => x.CreatedAt)
                             .AsQueryable();

            // Sort
            if (filter.ColumnName != null && filter.Direction != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
            }

            // Panigation query
            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var result = await panigationQuery.ToListAsync();
            return new PaginatedResponse<AttachmentDTO>
            {
                PageSize = filter.PageSize,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                Data = result,
                TotalRecords = totalRecords

            };
        }
        public async Task<RequestExpenses> UpdateExpenses(RequestExpenses expenses,string CompanyId, string userId)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var dataExpenses = _context.Expenses.Include(o => o.ItemExpense)
                                                 .ThenInclude(x => x.Taxes)
                                                 .FirstOrDefault(p => p.Id == expenses.Id);
                    if (dataExpenses == null)
                    {
                        return null;
                    }
                    else
                    {
                        var existingAttachments = _context.Attachment.Where(x => x.ExpensesId == expenses.Id).ToList();

                        var listRemoveAttchExpenses = existingAttachments
                                                     .Where(x => !expenses.Attachments.Select(p => p.Id).Contains(x.Id))
                                                     .ToList();

                        var existingItems = _context.ItemExpense.Include(x => x.Taxes).Where(x => x.ExpensesId == expenses.Id).ToList();
                        var listRemoveItem = existingItems.Where(x => !expenses.ItemExpense.Select(p => p.Id).Contains(x.Id))
                                                          .ToList();

                        // remove  tax
                        var listTax = existingItems.SelectMany(c => c.Taxes);
                        var addTaxes = expenses.ItemExpense.SelectMany(c => c.Taxes).Select(c => c.Id);
                        var listRemoveTax = listTax.Where(x => !addTaxes.Contains(x.Id)).ToList();
                        _context.Taxs.RemoveRange(listRemoveTax);

                        // remove itemExpenses
                        _context.ItemExpense.RemoveRange(listRemoveItem);

                        // remove AttchExpense
                        _context.Attachment.RemoveRange(listRemoveAttchExpenses);

                        // update item expenses
                        if (expenses.ItemExpense.Count > 0)
                        {
                            foreach (var newItemExpenses in expenses.ItemExpense)
                            {
                                var existing = existingItems.FirstOrDefault(e => e.Id == newItemExpenses.Id);
                                if (existing != null)
                                {
                                    // Update existing item
                                    existing.description = newItemExpenses.description;
                                    existing.qty = newItemExpenses.qty;
                                    existing.total = newItemExpenses.total;
                                    existing.rate = newItemExpenses.rate;
                                    foreach (var tax in newItemExpenses.Taxes)
                                    {
                                        var existingTax = listTax.FirstOrDefault(e => e.ItemExpenseId == newItemExpenses.Id && e.CompanyTaxId == tax.CompanyTaxId);
                                        if (existingTax != null)
                                        {
                                            existingTax.CompanyTaxId = tax.CompanyTaxId;
                                            tax.ItemExpenseId = newItemExpenses.Id;
                                            existingTax.UpdatedBy = userId.ToString();
                                        }
                                        else
                                        {
                                            tax.CreatedBy = userId.ToString();
                                            tax.CompanyTaxId = tax.Id;
                                            tax.ItemExpenseId = newItemExpenses.Id;
                                            tax.Id = Guid.Empty;
                                            _context.Taxs.Add(tax);
                                        }
                                    }
                                }
                                else
                                {
                                    // Add new item
                                    newItemExpenses.Taxes.ForEach(item =>
                                    {
                                        item.CompanyTaxId = item.Id;
                                        item.Id = Guid.Empty;
                                        item.CreatedBy = userId.ToString();
                                    });
                                    newItemExpenses.UpdatedBy = userId.ToString();
                                    newItemExpenses.ExpensesId = (Guid)expenses.Id;
                                    _context.ItemExpense.Add(newItemExpenses);
                                }
                            }
                        }

                        // add Attch
                        var addedAttachments = expenses.Attachments
                        .Where(a => !existingAttachments.Any(c => a.Id == c.Id))
                        .ToList();

                        if (addedAttachments.Count > 0)
                        {
                            foreach (var item in addedAttachments)
                            {
                                item.ExpensesId = expenses.Id;
                                item.CreatedBy = userId;
                                var fileConvert = DigitalOcean.Base64ToIFormFile(item.base64, item.Filename, item.Type);
                                var result = await DigitalOcean.UploadFileAsync(fileConvert, config, CompanyId);

                            }
                            _context.Attachment.AddRange(addedAttachments);
                        }

                        dataExpenses.UpdatedBy = userId;
                        dataExpenses.ExpensesName = expenses.ExpensesName;
                        dataExpenses.ClientId = expenses.ClientId;
                        dataExpenses.ProjectId = expenses.ProjectId;
                        dataExpenses.CategoryId = expenses.CategoryId;
                        dataExpenses.MerchantId = expenses.MerchantId;
                        dataExpenses.CategoryItemId = expenses.CategoryItemId;
                        dataExpenses.Note = expenses.Note;
                        dataExpenses.Date = expenses.Date;
                        dataExpenses.PaidAmount = expenses.PaidAmount;
                        await _context.SaveChangesAsync();
                        await transaction.CommitAsync();
                        return expenses;
                    }
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        public async Task<DTOExpensesDetail> GetExpensesById(string Id)
        {
            var data = await _context.Expenses.AsNoTracking()
                                 .OrderByDescending(x => x.CreatedAt)
                                .Include(x => x.Category)
                                .Include(x => x.CategoryItem)
                                .Include(x => x.Merchant)
                                .Include(x => x.Client)
                                .Include(x => x.Project)
                                .Include(x => x.Attachments)
                                .Include(x => x.ItemExpense)
                                .ThenInclude(c => c.Taxes).ThenInclude(x => x.CompanyTax)
                                 .Select(c => new DTOExpensesDetail
                                 {
                                     Id = c.Id,
                                     ProjectId=c.ProjectId,
                                     CategoryId = c.Category.Id,
                                     ExpensesName=c.ExpensesName,
                                     ClientId = c.ClientId,
                                     CategoryItemId = c.CategoryItemId,
                                     MerchantId = c.MerchantId,
                                     Date = c.Date,
                                     PaidAmount = c.PaidAmount,
                                     Note = c.Note,
                                     Attachments = c.Attachments.Select(a => new AttachmentDTO
                                     {
                                         Id = a.Id,
                                         Type = a.Type,
                                         Filename = a.Filename,
                                         Size = a.Size.Value // Need for update process
                                     }).ToList(),

                                     ItemExpenses = c.ItemExpense
                                 })
                                .FirstOrDefaultAsync(x => x.Id.ToString() == Id);
            return data;
        }

        public async Task<bool> MarkAsPaid(string Id)
        {
            try
            {
                var data = await _context.Expenses.FindAsync(Guid.Parse(Id));
                data.DatePaid = DateTime.UtcNow;
                data.Status = (int)ExpensesEnum.BILLED;
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<DTOItemExpense>> GetExpenseItemsByExpenseId(string expenseId)
        {
            var expenseGuid = Guid.Parse(expenseId);

            var itemExpenses = await _context.ItemExpense
                .AsNoTracking()
                .Where(ie => ie.ExpensesId == expenseGuid)
                .Include(ie => ie.Taxes)
                .ThenInclude(t => t.CompanyTax)
                .OrderBy(ie => ie.CreatedAt)
                .Select(ie => new DTOItemExpense
                {
                    Id = ie.Id,
                    ExpensesId = ie.ExpensesId,
                    Rate = ie.rate,
                    Qty = ie.qty,
                    Total = ie.total,
                    Description = ie.description,
                    CreatedAt = ie.CreatedAt,
                    UpdatedAt = ie.UpdatedAt ?? DateTime.UtcNow,
                    CreatedBy = ie.CreatedBy,
                    UpdatedBy = ie.UpdatedBy,
                    Taxes = ie.Taxes.Select(t => new TaxItem
                    {
                        Id = t.Id,
                        CompanyTaxId = t.CompanyTaxId,
                        Name = t.CompanyTax.Name,
                        TaxeNumber = t.CompanyTax.TaxeNumber,
                        Amount = t.CompanyTax.Amount
                    }).ToList()
                })
                .ToListAsync();

            return itemExpenses;
        }
    }
}
