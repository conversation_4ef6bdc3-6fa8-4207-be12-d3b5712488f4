# Test Plan for GetExpenseItemsByExpenseId API

## API Endpoint
- **URL**: `GET /api/Expenses/GetExpenseItemsByExpenseId?expenseId={expenseId}`
- **Method**: GET
- **Authorization**: Required (Bearer token)
- **Roles**: <PERSON><PERSON>, Manager, Accountant, Employee

## Request Parameters
- `expenseId` (string, required): The GUID of the expense to get items for

## Expected Response
```json
[
  {
    "id": "guid",
    "expensesId": "guid", 
    "rate": 0.00,
    "qty": 0.00,
    "total": 0.00,
    "description": "string",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z", 
    "createdBy": "string",
    "updatedBy": "string",
    "taxes": [
      {
        "id": "guid",
        "name": "string",
        "total": 0.00,
        "taxeNumber": "string", 
        "amount": 0.00,
        "companyTaxId": "guid"
      }
    ]
  }
]
```

## Test Cases

### 1. Valid Expense ID with Items
- **Input**: Valid expense ID that has associated expense items
- **Expected**: 200 OK with array of expense items

### 2. Valid Expense ID with No Items  
- **Input**: Valid expense ID that has no associated expense items
- **Expected**: 200 OK with empty array

### 3. Invalid Expense ID
- **Input**: Non-existent expense ID
- **Expected**: 200 OK with empty array

### 4. Invalid GUID Format
- **Input**: Malformed GUID string
- **Expected**: 400 Bad Request or 500 Internal Server Error

### 5. Unauthorized Access
- **Input**: Request without valid authorization token
- **Expected**: 401 Unauthorized

### 6. Insufficient Permissions
- **Input**: Request from user without required role
- **Expected**: 403 Forbidden

## Manual Testing Steps

1. Start the application
2. Authenticate and get a valid bearer token
3. Create or find an existing expense with items
4. Call the API with the expense ID
5. Verify the response structure and data
6. Test edge cases (empty results, invalid IDs, etc.)

## Implementation Details

- **Controller**: `ExpensesController.GetExpenseItemsByExpenseId`
- **Service**: `ExpensesService.GetExpenseItemsByExpenseId`
- **DTO**: `DTOItemExpense`
- **Entity**: `ItemExpense` with `Tax` navigation properties
