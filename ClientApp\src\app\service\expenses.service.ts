import { environment } from 'environments/environment';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { GetExpenseQueryParam, GetUploadedExpensesQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { Observable } from 'rxjs';
import { Expenses } from 'app/dto/interface/expenses.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { Attachment } from '../dto/interface/attachment.interface';
import { DTOExpensesDetail } from 'app/dto/DTOExpensesDetail.dto';
import { ItemExpense } from '../dto/interface/ItemExpense.interface';
import { currentTimeZone, formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class ExpensesService {
  private http = inject(HttpClient)
  constructor() { }

  CreateExpenses(payload: any): Observable<Expenses> {
    return this.http.post<Expenses>(UrlApi + '/Expenses/CreateExpenses', payload);
  }
  UpdateExpenses(payload: any): Observable<Expenses> {
    return this.http.post<Expenses>(UrlApi + '/Expenses/UpdateExpenses', payload);
  }
  GetExpensesById(Id: string): Observable<DTOExpensesDetail> {
    return this.http.get<DTOExpensesDetail>(UrlApi + `/Expenses/GetExpensesById?Id=${Id}`);
  }

  GetAllExpenses(params: GetExpenseQueryParam): Observable<PaginatedResponse<Expenses>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<Expenses>>(UrlApi + `/Expenses/GetAllExpenses`, { params: query });
  }
  GetAllUploadExpenses(params: GetUploadedExpensesQueryParam): Observable<PaginatedResponse<Attachment>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<Attachment>>(UrlApi + `/Expenses/GetAllUploadExpenses`, { params: query });
  }

  DeleteFileExpenses(payload: any): Observable<object> {
    return this.http.post(UrlApi + '/Expenses/DeleteExpenses', payload);
  }
  DeleteExpenses(payload: any) {
    return this.http.post(UrlApi + '/Expenses/DeleteExpenses', payload);
  }
  MarkAsPaid(id: string) {
    return this.http.put(UrlApi + `/Expenses/MarkAsPaid?Id=${id}`, null);
  }

  GetExpenseItemsByExpenseId(expenseId: string): Observable<ItemExpense[]> {
    return this.http.get<ItemExpense[]>(UrlApi + `/Expenses/GetExpenseItemsByExpenseId?expenseId=${expenseId}`);
  }

}
